// src/lib/stripe/plans.ts
// Configuración de planes (solo datos, sin instancia de Stripe)

export const PLANS = {
  free: {
    id: 'free',
    name: '<PERSON> Gratis',
    price: 0,
    stripeProductId: null,
    stripePriceId: null,
    features: [
      'Incluye:',
      '• Uso de la plataforma solo durante 5 días',
      '• Subida de documentos: máximo 1 documento',
      '• Generador de test: máximo 10 preguntas test',
      '• Generador de flashcards: máximo 10 tarjetas flashcard',
      '• Generador de mapas mentales: máximo 2 mapas mentales',
      'No incluye:',
      '• Planificación de estudios',
      '• Habla con tu preparador IA',
      '• Resúmenes A2 y A1',
    ],
    limits: {
      documents: 1,
      mindMapsPerWeek: 2,
      testsPerWeek: 10,
      flashcardsPerWeek: 10,
    }
  },
  usuario: {
    id: 'usuario',
    name: 'Plan Usuario',
    price: 1000, // En centavos (€10.00)
    stripeProductId: 'prod_SR65BdKdek1OXd',
    stripePriceId: 'price_1RWE1807kFn3sIXhlnKAfLoV',
    features: [
      'Incluye:',
      '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',
      '• Subida de documentos',
      '• Habla con tu preparador IA *',
      '• Generador de test *',
      '• Generador de flashcards *',
      '• Generador de mapas mentales *',
      '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',
      'No incluye:',
      '• Planificación de estudios',
      '• Resúmenes A2 y A1',
    ],
    limits: {
      documents: -1, // Ilimitado
      mindMapsPerWeek: -1, // Ilimitado (limitado por tokens)
      testsPerWeek: -1, // Ilimitado (limitado por tokens)
      flashcardsPerWeek: -1, // Ilimitado (limitado por tokens)
      monthlyTokens: 1000000, // 1 millón de tokens mensuales
    }
  },
  pro: {
    id: 'pro',
    name: 'Plan Pro',
    price: 1500, // En centavos (€15.00)
    stripeProductId: 'prod_SR66U2G7bVJqu3',
    stripePriceId: 'price_1RWE7l07kFn3sIXh1rCiXMqp',
    features: [
      'Incluye:',
      '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',
      '• Subida de documentos',
      '• Planificación de estudios mediante IA*',
      '• Habla con tu preparador IA *',
      '• Generador de test *',
      '• Generador de flashcards *',
      '• Generador de mapas mentales *',
      '• Generación de Resúmenes para A2 y A1',
      '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',
    ],
    limits: {
      documents: -1, // Ilimitado
      mindMapsPerWeek: -1, // Ilimitado (limitado por tokens)
      testsPerWeek: -1, // Ilimitado (limitado por tokens)
      flashcardsPerWeek: -1, // Ilimitado (limitado por tokens)
      monthlyTokens: 1000000, // 1 millón de tokens mensuales
    }
  }
} as const;

export type PlanId = keyof typeof PLANS;

// Función para obtener plan por ID
export function getPlanById(planId: string): typeof PLANS[PlanId] | null {
  return PLANS[planId as PlanId] || null;
}

// Función para validar si un plan es válido
export function isValidPlan(planId: string): planId is PlanId {
  return planId in PLANS;
}

// URLs de la aplicación
export const APP_URLS = {
  success: `${process.env.NEXT_PUBLIC_APP_URL}/thank-you`,
  cancel: `${process.env.NEXT_PUBLIC_APP_URL}/payment`,
  webhook: `${process.env.NEXT_PUBLIC_APP_URL}/api/stripe/webhook`,
} as const;
