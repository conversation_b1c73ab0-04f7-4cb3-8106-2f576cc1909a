// src/__tests__/webhook-test.ts
// Prueba básica para verificar que los webhooks funcionan

import { StripeWebhookHandlers } from '@/lib/services/stripeWebhookHandlers';

// Mock de datos de Stripe para pruebas
const mockCheckoutSession = {
  id: 'cs_test_123456789',
  payment_status: 'paid',
  amount_total: 1000,
  currency: 'eur',
  customer: 'cus_test_123',
  subscription: null,
  metadata: {
    planId: 'usuario',
    customerEmail: '<EMAIL>',
    customerName: 'Test User',
    source: 'test'
  }
} as any;

// Función de prueba básica
export async function testWebhookHandlers() {
  console.log('🧪 Iniciando pruebas de webhooks...');
  
  try {
    // Test 1: Verificar que el manejador no falla
    console.log('📝 Test 1: Verificando manejador de checkout.session.completed');
    
    const result = await StripeWebhookHandlers.handleCheckoutSessionCompleted(mockCheckoutSession);
    
    console.log('✅ Resultado del test:', {
      success: result.success,
      message: result.message,
      hasError: !!result.error
    });
    
    if (result.success) {
      console.log('🎉 Test básico de webhook PASADO');
    } else {
      console.log('⚠️ Test básico de webhook FALLÓ:', result.error);
    }
    
    return result.success;
    
  } catch (error) {
    console.error('❌ Error en test de webhook:', error);
    return false;
  }
}

// Ejecutar test si se llama directamente
if (require.main === module) {
  testWebhookHandlers().then(success => {
    process.exit(success ? 0 : 1);
  });
}
