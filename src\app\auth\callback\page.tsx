// src/app/auth/callback/page.tsx
// Página de callback para autenticación de Supabase

'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Procesando autenticación...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase = createClient();
        
        // Obtener código de autenticación de la URL
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');
        
        if (error) {
          console.error('Auth error:', error, errorDescription);
          setStatus('error');
          setMessage(errorDescription || 'Error en la autenticación');
          return;
        }
        
        if (code) {
          // Intercambiar código por sesión
          const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);
          
          if (exchangeError) {
            console.error('Error exchanging code:', exchangeError);
            setStatus('error');
            setMessage('Error procesando la autenticación');
            return;
          }
          
          if (data.user) {
            setStatus('success');
            setMessage('¡Autenticación exitosa! Redirigiendo...');
            
            // Obtener información del plan del usuario
            const { data: profile } = await supabase
              .from('user_profiles')
              .select('subscription_plan, payment_verified')
              .eq('user_id', data.user.id)
              .single();
            
            // Redirigir según el estado del usuario
            setTimeout(() => {
              if (profile?.payment_verified) {
                // Usuario con pago verificado -> ir a la aplicación
                const transactionId = searchParams.get('transaction');
                const planId = profile.subscription_plan;
                
                router.push(`/welcome?plan=${planId}&transaction=${transactionId}`);
              } else {
                // Usuario sin pago verificado -> ir a pago
                router.push('/payment');
              }
            }, 2000);
          }
        } else {
          // No hay código, verificar si ya está autenticado
          const { data: { user } } = await supabase.auth.getUser();
          
          if (user) {
            setStatus('success');
            setMessage('Ya estás autenticado. Redirigiendo...');
            
            setTimeout(() => {
              router.push('/app');
            }, 1000);
          } else {
            setStatus('error');
            setMessage('No se encontró información de autenticación');
          }
        }
        
      } catch (error) {
        console.error('Callback error:', error);
        setStatus('error');
        setMessage('Error procesando la autenticación');
      }
    };

    handleAuthCallback();
  }, [router, searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        {status === 'loading' && (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Procesando...</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}
        
        {status === 'success' && (
          <>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">¡Éxito!</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}
        
        {status === 'error' && (
          <>
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Error</h2>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                Ir a Login
              </button>
              <button
                onClick={() => router.push('/payment')}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
              >
                Ir a Planes
              </button>
            </div>
          </>
        )}
        
        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            ¿Problemas? <a href="/contact" className="text-blue-600 hover:underline">Contacta soporte</a>
          </p>
        </div>
      </div>
    </div>
  );
}
