// src/lib/services/stripeWebhookHandlers.ts
// Manejadores específicos para eventos de webhooks de Stripe

import Strip<PERSON> from 'stripe';
import { UserManagementService } from './userManagement';
import { SupabaseAdminService, supabaseAdmin } from '@/lib/supabase/admin';
import { WebhookLogger } from '@/lib/utils/webhookLogger';

export interface WebhookHandlerResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class StripeWebhookHandlers {
  
  /**
   * Manejar evento checkout.session.completed
   * Se ejecuta cuando un pago se completa exitosamente
   */
  static async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<WebhookHandlerResult> {
    try {
      console.log('🎯 Procesando checkout.session.completed:', session.id);
      
      // Validar que el pago esté completado
      if (session.payment_status !== 'paid') {
        return {
          success: false,
          message: 'Payment not completed',
          error: `Payment status: ${session.payment_status}`
        };
      }
      
      // Extraer metadata
      const { planId, customerEmail, customerName } = session.metadata || {};
      
      if (!planId || !customerEmail) {
        return {
          success: false,
          message: 'Missing required metadata',
          error: 'planId and customerEmail are required'
        };
      }
      
      // Verificar si ya fue procesado
      const existingTransaction = await SupabaseAdminService.getTransactionBySessionId(session.id);
      if (existingTransaction) {
        console.log('⚠️ Transacción ya procesada:', existingTransaction.id);
        return {
          success: true,
          message: 'Transaction already processed',
          data: { transactionId: existingTransaction.id }
        };
      }
      
      // Crear usuario con plan
      const result = await UserManagementService.createUserWithPlan({
        email: customerEmail,
        name: customerName,
        planId: planId,
        stripeSessionId: session.id,
        stripeCustomerId: session.customer as string,
        amount: session.amount_total || 0,
        currency: session.currency || 'eur',
        subscriptionId: session.subscription as string
      });
      
      if (!result.success) {
        return {
          success: false,
          message: 'Failed to create user',
          error: result.error
        };
      }
      
      console.log('✅ Usuario creado exitosamente desde webhook');
      
      return {
        success: true,
        message: 'User created successfully',
        data: {
          userId: result.userId,
          profileId: result.profileId,
          transactionId: result.transactionId
        }
      };
      
    } catch (error) {
      console.error('❌ Error en handleCheckoutSessionCompleted:', error);
      return {
        success: false,
        message: 'Internal error processing checkout session',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento payment_intent.succeeded
   * Para pagos únicos exitosos
   */
  static async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<WebhookHandlerResult> {
    try {
      console.log('💳 Procesando payment_intent.succeeded:', paymentIntent.id);
      
      // Para pagos únicos, la lógica principal está en checkout.session.completed
      // Aquí solo registramos el evento para auditoría
      
      return {
        success: true,
        message: 'Payment intent logged successfully',
        data: { paymentIntentId: paymentIntent.id }
      };
      
    } catch (error) {
      console.error('❌ Error en handlePaymentIntentSucceeded:', error);
      return {
        success: false,
        message: 'Error processing payment intent',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento customer.subscription.created
   * Cuando se crea una nueva suscripción
   */
  static async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<WebhookHandlerResult> {
    try {
      console.log('🔄 Procesando customer.subscription.created:', subscription.id);
      
      // Obtener información del cliente
      const customerId = subscription.customer as string;
      const planId = subscription.metadata?.planId;
      
      if (!planId) {
        return {
          success: false,
          message: 'Missing plan ID in subscription metadata'
        };
      }
      
      // Actualizar información de suscripción en la transacción
      const { error } = await supabaseAdmin
        .from('stripe_transactions')
        .update({
          subscription_id: subscription.id,
          metadata: {
            subscription_status: subscription.status,
            current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
          }
        })
        .eq('stripe_customer_id', customerId);
      
      if (error) {
        console.error('Error actualizando transacción con suscripción:', error);
      }
      
      return {
        success: true,
        message: 'Subscription created successfully',
        data: { subscriptionId: subscription.id }
      };
      
    } catch (error) {
      console.error('❌ Error en handleSubscriptionCreated:', error);
      return {
        success: false,
        message: 'Error processing subscription creation',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento customer.subscription.updated
   * Cuando se actualiza una suscripción
   */
  static async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<WebhookHandlerResult> {
    try {
      console.log('🔄 Procesando customer.subscription.updated:', subscription.id);
      
      // Obtener usuario por customer ID
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, subscription_plan')
        .eq('stripe_customer_id', subscription.customer)
        .single();
      
      if (!profile) {
        return {
          success: false,
          message: 'User profile not found for customer'
        };
      }
      
      // Actualizar estado de la suscripción
      const { error } = await supabaseAdmin
        .from('user_profiles')
        .update({
          plan_expires_at: new Date(subscription.current_period_end * 1000).toISOString(),
          updated_at: new Date().toISOString(),
          security_flags: {
            subscription_status: subscription.status,
            last_updated: new Date().toISOString()
          }
        })
        .eq('user_id', profile.user_id);
      
      if (error) {
        console.error('Error actualizando perfil de usuario:', error);
        return {
          success: false,
          message: 'Error updating user profile'
        };
      }
      
      return {
        success: true,
        message: 'Subscription updated successfully',
        data: { subscriptionId: subscription.id }
      };
      
    } catch (error) {
      console.error('❌ Error en handleSubscriptionUpdated:', error);
      return {
        success: false,
        message: 'Error processing subscription update',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento customer.subscription.deleted
   * Cuando se cancela una suscripción
   */
  static async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<WebhookHandlerResult> {
    try {
      console.log('❌ Procesando customer.subscription.deleted:', subscription.id);
      
      // Obtener usuario por customer ID
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, subscription_plan')
        .eq('stripe_customer_id', subscription.customer)
        .single();
      
      if (!profile) {
        return {
          success: false,
          message: 'User profile not found for customer'
        };
      }
      
      // Downgrade a plan gratuito
      const result = await UserManagementService.updateUserPlan(
        profile.user_id,
        'free',
        undefined,
        'Subscription cancelled'
      );
      
      if (!result.success) {
        return {
          success: false,
          message: 'Error downgrading user plan',
          error: result.error
        };
      }
      
      return {
        success: true,
        message: 'Subscription cancelled and user downgraded to free plan',
        data: { userId: profile.user_id }
      };
      
    } catch (error) {
      console.error('❌ Error en handleSubscriptionDeleted:', error);
      return {
        success: false,
        message: 'Error processing subscription deletion',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento invoice.payment_succeeded
   * Para renovaciones de suscripción
   */
  static async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<WebhookHandlerResult> {
    try {
      console.log('💰 Procesando invoice.payment_succeeded:', invoice.id);
      
      if (!invoice.subscription) {
        return {
          success: true,
          message: 'Invoice not related to subscription, skipping'
        };
      }
      
      // Actualizar fecha de último pago
      const { error } = await supabaseAdmin
        .from('user_profiles')
        .update({
          last_payment_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('stripe_customer_id', invoice.customer);
      
      if (error) {
        console.error('Error actualizando fecha de pago:', error);
      }
      
      return {
        success: true,
        message: 'Invoice payment processed successfully',
        data: { invoiceId: invoice.id }
      };
      
    } catch (error) {
      console.error('❌ Error en handleInvoicePaymentSucceeded:', error);
      return {
        success: false,
        message: 'Error processing invoice payment',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
